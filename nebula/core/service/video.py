import json
import base64
import uuid
import os
import tempfile
import aiofiles
import asyncio
from datetime import datetime
from typing import List, Optional, Dict, Any
from bson import ObjectId
import aiohttp
from nebula.core.repository.video import Entity, VideoRepository
from nebula.core.infrastructure import (
    singleton, Settings,
    NebulaException, NotFoundError, ValidationError, ConfigError, ExternalServiceError, YoutubeException, TimeoutError,
    get_logger, log_context, control
)
from nebula.core.infrastructure import provider

logger = get_logger(__name__)

# 定义User-Agent
USER_AGENT = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36"

@singleton
class VideoService:
    """视频服务

    提供视频资源的管理、查询和处理功能。
    """

    def __init__(self, settings: Settings = None):
        """初始化视频服务

        Args:
            settings: 应用配置
        """
        if not settings.video or not settings.video.youtube_proxy or not settings.video.twitter_proxy:
            raise ConfigError("********** Video服务配置项缺失 **********")

        self.repo = VideoRepository()
        self.settings = settings
        from nebula.core.infrastructure import storage
        from nebula.core.infrastructure import gemini
        from nebula.core.infrastructure import browser
        self.storage = provider.get(storage.StorageService)
        self.gemini = provider.get(gemini.GeminiService)
        self.browser = provider.get(browser.BrowserService)
        self.bucket = self.settings.video.bucket

    async def get_video(self, id: str) -> Entity.Video:
        """获取单个视频详情

        Args:
            id: 视频ID

        Returns:
            视频实体

        Raises:
            NotFoundError: 视频不存在
        """
        video = await self.repo.find_one({"_id": ObjectId(id)})
        if not video:
            raise NotFoundError("Video",f"id={id}")
        return video

    async def list_videos(
        self, keyword: str = None, tags: List[str] = None,
        category: str = None, skip: int = 0, limit: int = 20,
        sort_field: str = "updated_at", sort_order: int = -1
    ) -> List[Entity.Video]:
        """获取视频列表

        Args:
            keyword: 搜索关键词
            tags: 标签过滤
            category: 类别过滤
            skip: 跳过数量
            limit: 返回限制
            sort_field: 排序字段，默认为"updated_at"
            sort_order: 排序方式，1为升序，-1为降序，默认为-1（降序）

        Returns:
            视频列表
        """
        query = {}

        if keyword:
            # 文本搜索
            query["$or"] = [
                {"title": {"$regex": keyword, "$options": "i"}},
                {"video_desc": {"$regex": keyword, "$options": "i"}},
                {"overview": {"$regex": keyword, "$options": "i"}},
                {"subtitles": {"$regex": keyword, "$options": "i"}}
            ]

        if tags:
            # 标签过滤
            query["tags"] = {"$in": tags}

        if category:
            # 类别过滤
            query["category"] = category

        return await self.repo.find_many(query, skip, limit, sort_field, sort_order)

    async def create_video(self, video: Entity.Video) -> Entity.Video:
        """创建新视频

        Args:
            video: 视频实体

        Returns:
            创建的视频实体
        """
        id = await self.repo.save(video)
        return await self.get_video(id)

    @control(retry_probability=0.8, retry_message="故意抛出异常")
    async def update_video(self, id: str, data: Dict[str, Any]) -> Entity.Video:
        """更新视频信息

        Args:
            id: 视频ID
            data: 更新数据

        Returns:
            更新后的视频实体

        Raises:
            NotFoundError: 视频不存在
            DebugRetryException: 控制装饰器可能抛出的调试异常
        """
        # 检查是否存在
        video = await self.repo.find_one({"_id": ObjectId(id)})
        if not video:
            raise NotFoundError("Video",f"id={id}")

        # 移除不可更新字段
        if "updated_at" in data:
            del data["updated_at"]

        # 更新字段
        for key, value in data.items():
            setattr(video, key, value)

        # 保存更新
        await self.repo.save(video)
        return await self.get_video(id)

    async def delete_video(self, id: str) -> bool:
        """删除视频

        Args:
            id: 视频ID

        Returns:
            是否成功删除

        Raises:
            NotFoundError: 视频不存在
        """
        # 检查是否存在
        video = await self.repo.find_one({"_id": ObjectId(id)})
        if not video:
            raise NotFoundError("Video",f"id={id}")

        # 删除视频和封面

        v_delete = await self.storage.delete_object(video.video_path, bucket_name=self.bucket)
        c_delete = await self.storage.delete_object(video.cover_path, bucket_name=self.bucket)

        # 从数据库删除
        return await self.repo.delete(id),v_delete,c_delete

    async def count_videos(self, keyword: str = None, tags: List[str] = None,
        category: str = None) -> int:
        """统计视频数量

        Args:
            keyword: 搜索关键词
            tags: 标签过滤
            category: 类别过滤

        Returns:
            视频数量
        """
        query = {}

        if keyword:
            # 文本搜索
            query["$or"] = [
                {"title": {"$regex": keyword, "$options": "i"}},
                {"video_desc": {"$regex": keyword, "$options": "i"}},
                {"overview": {"$regex": keyword, "$options": "i"}},
                {"subtitles": {"$regex": keyword, "$options": "i"}}
            ]

        if tags:
            # 标签过滤
            query["tags"] = {"$in": tags}

        if category:
            # 类别过滤
            query["category"] = category

        return await self.repo.count(query)

    async def add_tags(self, id: str, tags: List[str]) -> Entity.Video:
        """为视频添加标签

        Args:
            id: 视频ID
            tags: 标签列表

        Returns:
            更新后的视频实体
        """
        video = await self.get_video(id)

        # 添加新标签（去重）
        current_tags = set(video.tags)
        for tag in tags:
            current_tags.add(tag)

        video.tags = list(current_tags)
        await self.repo.save(video)

        return await self.get_video(id)

    async def remove_tags(self, id: str, tags: List[str]) -> Entity.Video:
        """移除视频标签

        Args:
            id: 视频ID
            tags: 要移除的标签列表

        Returns:
            更新后的视频实体
        """
        video = await self.get_video(id)

        # 移除标签
        video.tags = [tag for tag in video.tags if tag not in tags]
        await self.repo.save(video)

        return await self.get_video(id)

    async def update_subtitles(self, id: str, subtitles: str, chunks: List[str] = None) -> Entity.Video:
        """更新视频字幕

        Args:
            id: 视频ID
            subtitles: 完整字幕文本
            chunks: 字幕片段列表

        Returns:
            更新后的视频实体
        """
        video = await self.get_video(id)

        # 更新字幕
        video.subtitles = subtitles

        # 如果提供了字幕片段，也更新
        if chunks:
            video.subtitles_chunks = chunks

        await self.repo.save(video)

        return await self.get_video(id)

    @control(retry_probability=0.8, retry_message="创建视频时故意抛出异常")
    async def create_from_file_url(self, file_url: str, title: str = "", desc: str = "") -> Entity.Video:
        """从文件URL创建视频

        下载视频文件，然后创建视频记录
        步骤:
        1.下载视频到本地
        2.调用gemini理解视频生成必要的字段(传入本地文件路径)
        3.调用ffmpeg获取封面帧(通过Docker Remote API调用jrottenberg/ffmpeg来根据<1>里的秒数获取封面帧)
        4.调用 self.storage 上传封面
        5.判断file_url是否为storage上传的临时文件,如果是则移动到永久存储,否则下载这个file_url然后上传
        6.创建视频记录

        Args:
            file_url: 视频文件URL
            title: 视频标题
            desc: 视频描述

        Returns:
            创建的视频实体

        Raises:
            NebulaException: 创建视频失败
            DebugRetryException: 控制装饰器可能抛出的调试异常
        """
        # 1.下载视频到本地文件
        video_data = await self.download_video(file_url)
        # 保存到临时文件
        # 获取文件扩展名
        ext = os.path.splitext(file_url)[1] or ".mp4"
        async with aiofiles.tempfile.NamedTemporaryFile("wb", delete=False, suffix=ext) as temp_file:
            await temp_file.write(video_data)
            video_size = len(video_data)
            logger.info(f"视频(url:{file_url})下载成功 (size:{video_size})")

            # 2. 调用gemini理解视频生成必要的字段
            prompt = """请以专业、客观的方式分析以下视频标签。
        用"中文"生成以下内容：
        一个简洁且描述性强的标题，字数不超过80个字
        一段客观的内容概述（800字以内）,请勿以"本视频"/"该视频"/这种第三方视角描述
        3-5个相关的关键词标签
        3-5个核心摘要或关键亮点
        输出subtitles_chunks: 如果非中文,请翻译成中文(注意:每3~5句话合并成一个chunk，这样既不会过短影响语义理解，也不会过长影响向量检索性能)
        取出你认为最适合并且最能代表视频内容的关键一帧(cover_frame_time:HH:MM:SS.fff)作为封面.
        """
            fmt = {
                "title":"标题",
                "desc":"描述",
                "overview":"请用<第一人称>为该视频描述核心内容用做分享摘要",
                "highlights":["核心摘要或关键亮点1","核心摘要或关键亮点2","核心摘要或关键亮点3"],
                "location":"没有地点时为空值",
                "category":"分类",
                "sub_category":"子分类",
                "tags":["关键词1","关键词2"],
                "subtitles_chunks":["字幕1","字幕2","字幕3","字幕4"],
                "cover_frame_time":"00:18:22.068"
            }
            result = await self.gemini.generate_content_for_file(temp_file.name, prompt, use_structured_response=True, response_format=fmt)
            try:
                result = json.loads(result)
            except Exception as e:
                if 'Unterminated' in str(e):
                    logger.info(result)
                    logger.error("大模型生成结果被截断...", exc_info=True)
                    raise ExternalServiceError("GEMINI", "大模型生成结果被截断...", should_retry=True)

            # 3. 调用ffmpeg获取封面帧
            cover_frame_time = result.get("cover_frame_time", 0)
            cover_image = await self.extract_cover_frame(temp_file.name, cover_frame_time)

            # 4. 调用 self.storage 上传封面
            cover_path = self.settings.video.cover_path.format(platform="nebula", unique_id=str(uuid.uuid4()),ext=".jpg")
            cover_url = await self.storage.upload_bytes(cover_image, cover_path, bucket_name=self.bucket)

            # 5. 判断file_url是否为storage上传的临时文件,如果是则移动到永久存储,否则下载这个file_url然后上传
            meta = await self.storage.get_temp_meta_from_url(file_url)
            if meta and meta["is_temp"] == "true":
                video_size = meta["content-length"]
                video_path = self.settings.video.video_path.format(platform="nebula", unique_id=str(uuid.uuid4()), ext=".mp4")
                play_url = await self.storage.get_object_url(video_path, bucket_name=self.bucket)
                await self.storage.move_object(meta["object_name"], video_path, bucket_name=self.bucket)
            else:
                video_path = self.settings.video.video_path.format(platform="nebula", unique_id=str(uuid.uuid4()), ext=".mp4")
                play_url = await self.storage.upload_bytes(video_data, video_path, bucket_name=self.bucket)

            # 6. 创建视频记录
            video = Entity.Video(
                title=result["title"],
                video_desc=result["desc"],
                video_path=video_path,
                cover_path=cover_path,
                cover_url=cover_url,
                video_size=video_size,
                play_url=play_url,
                source_url=file_url,
                platform="",
                overview=result["overview"],
                highlights=result["highlights"],
                location=result["location"],
                category=result["category"],
                sub_category=result["sub_category"],
                tags=result["tags"],
                subtitles_chunks=result["subtitles_chunks"],
                generated=True
            )
            return await self.create_video(video)

    async def extract_cover_frame(self, file_path: str, cover_frame_time: str) -> bytes:
        """使用ffmpeg提取视频封面帧（通过Docker Remote API）"""
        # Docker Remote API 配置
        docker_api_url = "http://172.17.0.1:2375"

        # 创建临时文件
        temp_file_path = tempfile.mktemp(suffix='.jpg')
        logger.info(f"提取封面帧: {file_path} -> {temp_file_path} (cover_frame_time:{cover_frame_time})")

        # 构建ffmpeg命令
        command = f"-ss {cover_frame_time} -i {file_path} -vframes 1 -pix_fmt yuvj420p {temp_file_path}"
        logger.info(f"docker run -v /tmp:/tmp --rm jrottenberg/ffmpeg {command}")

        container_id = None

        try:
            async with aiohttp.ClientSession() as session:
                # 1. 创建容器
                container_config = {
                    "Image": "jrottenberg/ffmpeg",
                    "Cmd": command.split(),
                    "HostConfig": {
                        "AutoRemove": True,
                        "Binds": ["/tmp:/tmp:rw"]
                    }
                }

                async with session.post(
                    f"{docker_api_url}/containers/create",
                    json=container_config,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status != 201:
                        error_text = await response.text()
                        raise NebulaException(f"创建Docker容器失败: {error_text}")

                    container_info = await response.json()
                    container_id = container_info["Id"]
                    logger.info(f"创建Docker容器成功: {container_id}")

                # 2. 启动容器
                async with session.post(
                    f"{docker_api_url}/containers/{container_id}/start"
                ) as response:
                    if response.status != 204:
                        error_text = await response.text()
                        raise NebulaException(f"启动Docker容器失败: {error_text}")

                    logger.info(f"启动Docker容器成功: {container_id}")

                # 3. 等待容器执行完成
                async with session.post(
                    f"{docker_api_url}/containers/{container_id}/wait"
                ) as response:
                    if response.status != 200:
                        error_text = await response.text()
                        raise NebulaException(f"等待Docker容器执行失败: {error_text}")

                    wait_result = await response.json()
                    exit_code = wait_result.get("StatusCode", -1)

                    if exit_code != 0:
                        # 获取容器日志
                        async with session.get(
                            f"{docker_api_url}/containers/{container_id}/logs?stdout=true&stderr=true"
                        ) as log_response:
                            logs = await log_response.text()
                            raise NebulaException(f"Docker容器执行失败，退出码: {exit_code}, 日志: {logs}")

                    logger.info(f"Docker容器执行成功: {container_id}")

                # # 4. 获取容器日志
                # async with session.get(
                #     f"{docker_api_url}/containers/{container_id}/logs?stdout=true&stderr=true"
                # ) as response:
                #     logs = await response.text()
                #     logger.info(f"Docker容器日志: {logs}")

            # 读取临时文件内容
            with open(temp_file_path, 'rb') as f:
                cover_frame = f.read()

            logger.info(f"提取封面帧成功，大小: {len(cover_frame)} 字节")
            return cover_frame

        except Exception as e:
            logger.error(f"提取封面帧失败: {str(e)}")
            raise NebulaException(f"提取封面帧失败: {str(e)}")
        finally:
            # 删除临时文件
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

            # 如果容器还存在，尝试删除它
            if container_id:
                try:
                    async with aiohttp.ClientSession() as session:
                        await session.delete(f"{docker_api_url}/containers/{container_id}?force=true")
                        logger.info(f"删除Docker容器成功: {container_id}")
                except Exception as e:
                    logger.warning(f"删除Docker容器失败: {str(e)}")

    async def download_video(self, url: str) -> bytes:
        """下载视频文件"""
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                # 判断content-type是否为视频类型
                if not response.content_type.startswith("video/"):
                    raise NebulaException(f"不支持的文件类型: {response.content_type}")
                return await response.read()


    @control(retry_probability=0.5, retry_message="更新视频时故意抛出异常")
    async def update_content_analysis(
        self, video_id: str
    ) -> Entity.Video:
        """更新视频内容分析结果

        Args:
            id: 视频ID

        Returns:
            更新后的视频实体
        """
        
        video = await self.get_video(video_id)
        if not video:
            raise NotFoundError("Video",f"id={video_id}")
        player_url = await self.storage.get_object_url(video.video_path, bucket_name=self.bucket)
        prompt = """请以专业、客观的方式分析以下视频标签。
    用"中文"生成以下内容：
    一个简洁且描述性强的标题，字数不超过80个字
    一段客观的内容概述（800字以内）,请勿以"本视频"/"该视频"/这种第三方视角描述
    3-5个相关的关键词标签
    3-5个核心摘要或关键亮点
    输出subtitles_chunks: 如果非中文,请翻译成中文(注意:每3~5句话合并成一个chunk，这样既不会过短影响语义理解，也不会过长影响向量检索性能)
    """
        fmt = {
            "title":"标题",
            "overview":"请用<第一人称>为该视频描述核心内容用做分享摘要",
            "highlights":["核心摘要或关键亮点1","核心摘要或关键亮点2","核心摘要或关键亮点3"],
            "location":"没有地点时为空值",
            "category":"分类",
            "sub_category":"子分类",
            "tags":["关键词1","关键词2"],
            "subtitles_chunks":["字幕1","字幕2","字幕3","字幕4"],
        }
        if video.platform == "youtube":
            player_url = video.source_url # gemini可以直接处理youtube视频url
        result = await self.gemini.generate_content_for_file(player_url,prompt, use_structured_response=True, response_format=fmt)
        try:
            result = json.loads(result)
        except Exception as e:
            if 'Unterminated' in str(e):
                logger.info(result)
                logger.error("大模型生成结果被截断...", exc_info=True)
                raise ExternalServiceError("GEMINI", "大模型生成结果被截断...", should_retry=True)
        # 更新内容分析结果
        video.overview=result["overview"]
        video.highlights=result["highlights"]
        video.category=result["category"]
        video.sub_category=result["sub_category"]
        video.location=result["location"]
        video.tags=result["tags"]
        video.subtitles_chunks=result["subtitles_chunks"]
        video.generated = True

        await self.repo.save(video)

        return video

    async def get_videos_by_tags(self, tags: List[str], skip: int = 0, limit: int = 20, sort_field: str = "updated_at", sort_order: int = -1) -> List[Entity.Video]:
        """按标签获取视频列表

        Args:
            tags: 标签列表
            skip: 跳过数量
            limit: 返回限制
            sort_field: 排序字段，默认为"updated_at"
            sort_order: 排序方式，1为升序，-1为降序，默认为-1（降序）

        Returns:
            视频列表
        """
        return await self.list_videos(None, tags, None, skip, limit, sort_field, sort_order)

    async def extract_share_info(self, share_text: str, save: bool=False) -> Dict[str, Any]:
        """从分享文本中提取视频信息

        Args:
            share_text: 包含视频分享链接的文本
            down_to_s3: 是否下载到S3

        Returns:
            视频信息字典，包含标题、描述、封面URL和播放地址

        Raises:
            NebulaException: 获取视频信息失败
        """
        # 判断是抖音、YouTube还是Twitter链接
        if "douyin.com" in share_text or "抖音" in share_text:
            result = await self._extract_douyin_info(share_text)
            platform = "douyin"
        elif "youtube.com" in share_text or "youtu.be" in share_text:
            result = await self._extract_youtube_info(share_text)
            platform = "youtube"
        elif "twitter.com" in share_text or "x.com" in share_text:
            result = await self._extract_twitter_info(share_text)
            platform = "twitter"
        else:
            raise NebulaException("不支持的视频分享链接")

        # 确保结果中包含所有必要的字段
        if not result:
            raise NebulaException("无法提取视频信息")

        if save:

            await self.import_from_share(title=result.get("title"), desc=result.get("desc"), cover_url=result.get("cover_url"), player_url=result.get("player_url"), video_id=result.get("video_id"), watch_url=result.get("watch_url"), duration=result.get("duration"), from_platform=platform)


        # 构建统一的返回格式
        return {
            "video_id": result.get("video_id"),
            "title": result.get("title"),
            "desc": result.get("desc"),
            "duration": result.get("duration"),
            "cover_url": result.get("cover_url"),
            "player_url": result.get("player_url"),
            "watch_url": result.get("watch_url"),
            "from": platform
        }

    async def _extract_youtube_info(self, share_text: str) -> Dict[str, Any]:
        """从分享文本中提取YouTube视频信息

        Args:
            share_text: 包含YouTube链接的分享文本

        Returns:
            视频信息字典，包含标题、描述、封面URL和播放地址

        Raises:
            NotFoundError: 未找到YouTube视频链接
            NebulaException: 获取视频信息失败
        """
        import re
        # 从分享文本中提取YouTube视频ID
        # 支持多种常见的YouTube链接格式
        youtube_patterns = [
            r'(?:https?://)?(?:www\.)?youtube\.com/watch\?v=([\w-]+)',  # 标准链接
            r'(?:https?://)?(?:www\.)?youtube\.com/embed/([\w-]+)',       # 嵌入链接
            r'(?:https?://)?(?:www\.)?youtube\.com/v/([\w-]+)',          # 旧式链接
            r'(?:https?://)?(?:www\.)?youtube\.com/shorts/([\w-]+)',     # 短视频链接
            r'(?:https?://)?youtu\.be/([\w-]+)'                           # 缩短链接
        ]

        video_id = None
        for pattern in youtube_patterns:
            matches = re.search(pattern, share_text)
            if matches:
                video_id = matches.group(1)
                break

        if not video_id:
            raise NotFoundError("Youtube","video_id")

        # 使用配置的代理URL获取视频信息
        youtube_proxy = self.settings.video.youtube_proxy.format(id=video_id)

        # 使用aiohttp请求代理服务
        async with aiohttp.ClientSession(headers={"User-Agent": USER_AGENT}) as session:
            async with session.get(youtube_proxy) as response:
                if response.status != 200:
                    raise NebulaException(f"代理服务返回错误状态码: {response.status}", should_retry=True)

                # 解析响应数据
                data = await response.json()

                if not data or "error" in data:
                    error_msg = data.get("error", "未知错误")
                    raise NebulaException(f"获取YouTube视频信息失败: {error_msg}", should_retry=True)

                # 检查响应状态
                if data.get("errcode", -1) != 0:
                    error_msg = data.get("msg", "未知错误")
                    raise NebulaException(f"获取YouTube视频信息失败: {error_msg}", should_retry=True)

                # 提取必要的信息
                result = {
                    "video_id": data.get("vid", video_id),
                    "title": data.get("title", ""),
                    "desc": data.get("description", ""),
                    "cover_url": data.get("thumbnail", ""),
                    "player_url": data.get("player_url", ""),
                    "watch_url": data.get("watch_url", f"https://www.youtube.com/watch?v={video_id}"),
                    "duration": data.get("duration", 0),
                    "author": data.get("author", ""),
                    "view_count": data.get("views", 0),
                    "publish_date": data.get("published_date", ""),
                    "format": data.get("format", ""),
                    "size_mb": data.get("size_mb", 0)
                }

                # 获取视频截图
                if result["cover_url"]:
                    try:
                        async with session.get(result["cover_url"], headers={"User-Agent": USER_AGENT}) as img_response:
                            if img_response.status == 200:
                                img_data = await img_response.read()
                                screenshot, screenshot_url = await self.storage.upload_byte_to_temp(
                                    img_data,
                                    ".jpg",
                                    bucket_name=self.bucket
                                )
                                result["screenshot"] = screenshot
                                result["screenshot_url"] = screenshot_url
                    except Exception as e:
                        logger.warning(f"获取YouTube视频截图失败: {str(e)}")

                return result

    async def _extract_douyin_info(self, share_text: str) -> Dict[str, Any]:
        """获取抖音视频信息

        Args:
            video_url: 抖音视频URL

        Returns:
            包含视频描述、封面URL和播放地址的字典

        Raises:
            NebulaException: 获取视频信息失败
        """
        import re
        video_id = None

        urls = re.findall(r'https://v\.douyin\.com/[^\s]+/?', share_text)
        video_url = urls[0] if urls else None
        if video_url is None:
            raise NotFoundError("抖音视频链接","null")

        # 使用aiohttp进行异步HTTP请求
        async with aiohttp.ClientSession(headers={"User-Agent": USER_AGENT}) as session:
            async with session.get(video_url, allow_redirects=False) as response:
                if response.status == 302:
                    location = response.headers.get("Location", "")
                    if "ixigua.com/" in location:
                        raise NotFoundError("抖音视频链接",video_url)
                    else:
                        from urllib.parse import urlparse
                        video_id = urlparse(location).path.strip('/').split('/')[-1]
                        video_url = f'https://www.douyin.com/discover?modal_id={video_id}'
                        logger.info(f"抖音地址: {video_url}")
                else:
                    raise NebulaException(f"不支持该地址: {video_url}")

        # 使用BrowserService获取页面HTML和截图
        result = await self.browser.get_page_html(video_url, screenshot=True)

        logger.info(f"获取抖音视频页面 ({video_url}), 截图 ({bool(result.get('screenshot'))})")
        screenshot = None
        screenshot_url = None
        if result.get("screenshot"):
            screenshot, screenshot_url = await self.storage.upload_byte_to_temp(
                base64.b64decode(result["screenshot"]),
                ".png",
                bucket_name=self.bucket
            )

        # 使用BeautifulSoup解析HTML
        from bs4 import BeautifulSoup
        import urllib.parse

        soup = BeautifulSoup(result["html"], "html.parser")
        render_data = soup.select("#RENDER_DATA")

        if not render_data:
            raise NebulaException("未找到RENDER_DATA元素")

        script = str(render_data[0].string)
        data = json.loads(urllib.parse.unquote(script))
        # 提取视频信息
        info = data.get("app", {}).get("videoDetail")
        if info:
            desc = info["desc"]
            cover_url = info["video"]["coverUrlList"][0]
            player_url = info["video"]["playAddr"][0]["src"]

            # 处理相对URL
            if player_url[:2] == "//":
                player_url = "https:" + player_url

            # 提取更多信息
            title = info.get("desc", "")
            author = info.get("authorInfo", {}).get("nickname", "")
            duration = info.get("video", {}).get("duration", 0)

            # 构建结果字典，保持与YouTube提取结果的一致性
            result = {
                "video_id":video_id,
                "title": title,
                "desc": desc,
                "cover_url": cover_url,
                "player_url": player_url,
                "screenshot": screenshot,
                "screenshot_url": screenshot_url,
                "author": author,
                "duration": duration,
                "watch_url": f"https://www.douyin.com/video/{video_id}"
            }

            return result
        else:
            # 记录错误日志
            logger.error({
                "msg": "未能识别抖音HTML内容",
                "特征RENDER_DATA": "存在" if script else "不存在"
            })
            return None

    async def _extract_twitter_info(self, share_text: str) -> Dict[str, Any]:
        """从分享文本中提取Twitter视频信息

        Args:
            share_text: 包含Twitter链接的分享文本

        Returns:
            视频信息字典，包含标题、描述、封面URL和播放地址

        Raises:
            NotFoundError: 未找到Twitter视频链接
            NebulaException: 获取视频信息失败
        """
        import re
        # 从分享文本中提取Twitter视频ID
        # 支持多种常见的Twitter链接格式
        twitter_patterns = [
            r'(?:https?://)?(?:www\.)?twitter\.com/[^/]+/status/(\d+)',  # 标准Twitter链接
            r'(?:https?://)?(?:www\.)?x\.com/[^/]+/status/(\d+)'          # 新版X链接
        ]

        video_id = None
        for pattern in twitter_patterns:
            matches = re.search(pattern, share_text)
            if matches:
                video_id = matches.group(1)
                break

        if not video_id:
            raise NotFoundError("Twitter", "video_id")

        # 使用完整URL调用Twitter代理
        full_url = None
        for pattern in twitter_patterns:
            matches = re.search(pattern, share_text)
            if matches:
                full_url = matches.group(0)
                break

        if not full_url:
            # 如果无法提取完整URL，则使用视频ID构造URL
            full_url = f"https://twitter.com/i/status/{video_id}"

        # 构造代理URL，使用url参数
        twitter_proxy = self.settings.video.twitter_proxy.format(url=full_url)

        # 使用aiohttp请求代理服务
        async with aiohttp.ClientSession(headers={"User-Agent": USER_AGENT}) as session:
            async with session.get(twitter_proxy) as response:
                if response.status != 200:
                    raise NebulaException(f"代理服务返回错误状态码: {response.status}")

                # 解析响应数据
                data = await response.json()

                if not data or "error" in data:
                    error_msg = data.get("error", "未知错误")
                    raise NebulaException(f"获取Twitter视频信息失败: {error_msg}")

                # 检查响应状态
                if data.get("errcode", -1) != 0:
                    error_msg = data.get("msg", "未知错误")
                    raise NebulaException(f"获取Twitter视频信息失败: {error_msg}")

                # 提取必要的信息
                result = {
                    "video_id": data.get("vid", video_id),
                    "title": f"{data.get('author', '')} - {data.get('description', '')}",
                    "desc": data.get("description", ""),
                    "cover_url": data.get("thumbnail", ""),
                    "player_url": data.get("player_url", ""),
                    "watch_url": data.get("watch_url", f"https://twitter.com/i/status/{video_id}"),
                    "duration": int(data.get("duration", 0) * 1000 / 1000),
                    "author": data.get("author", ""),
                    "view_count": data.get("views", 0),
                    "publish_date": data.get("published_date", "")
                }

                # 获取视频截图
                if result["cover_url"]:
                    try:
                        async with session.get(result["cover_url"], headers={"User-Agent": USER_AGENT}) as img_response:
                            if img_response.status == 200:
                                img_data = await img_response.read()
                                screenshot, screenshot_url = await self.storage.upload_byte_to_temp(
                                    img_data,
                                    ".jpg",
                                    bucket_name=self.bucket
                                )
                                result["screenshot"] = screenshot
                                result["screenshot_url"] = screenshot_url
                    except Exception as e:
                        logger.warning(f"获取Twitter视频截图失败: {str(e)}")

                return result

    @control(retry_probability=0.5, retry_message="导入视频时故意抛出异常")
    async def import_from_share(self, title: str, desc: str, cover_url: str, player_url: str, video_id: str, watch_url: str, duration: int = 0, from_platform: str = "") -> Entity.Video:
        """从分享链接导入视频

        下载视频封面和视频文件，然后创建视频记录

        Args:
            title: 视频标题
            desc: 视频描述
            cover_url: 视频封面URL
            player_url: 视频播放URL
            video_id: 视频ID
            watch_url: 视频观看URL
            duration: 视频时长
            from_platform: 视频来源平台

        Returns:
            创建的视频实体

        Raises:
            NebulaException: 导入视频失败
            DebugRetryException: 控制装饰器可能抛出的调试异常
        """
        # 生成唯一文件名
        if not player_url:
            raise NebulaException("视频播放地址不能为空")
        if not cover_url:
            raise NebulaException("视频封面地址不能为空")

        # 创建基于日期的目录结构
        unique_id = str(uuid.uuid4())
        video_size = 0

        # 下载封面图片
        cover_path = ""
        s3_cover_url = None
        # 下载图片
        logger.info(f"开始下载封面图片: {cover_url}")

        try:
            # 使用browser服务获取资源，避免403错误
            resource_result = await self.browser.get_resource_bytes(cover_url)

            if resource_result and resource_result.get("data"):
                # 从内容类型中获取图片扩展名
                content_type = resource_result.get("content_type", "")
                ext = {"image/jpeg": '.jpg', "image/png": ".png", "image/webp": ".webp", "image/gif": ".gif"}.get(content_type, '.png')

                # 更新cover_path
                cover_path = self.settings.video.cover_path.format(platform=from_platform, unique_id=unique_id,ext=ext)
                img_data = resource_result["data"]
                # 上传到存储服务
                s3_cover_url = await self.storage.upload_bytes(
                    data=img_data,
                    object_name=cover_path,
                    bucket_name=self.bucket
                )
                logger.info(f"封面图片下载成功, 已保存到存储服务({s3_cover_url})")
            else:
                raise NebulaException("使用browser服务获取资源失败")

        except Exception as e:
            # 如果browser服务失败，尝试使用直接HTTP请求
            logger.error(f"使用browser服务获取资源失败，尝试直接HTTP请求: {str(e)}", exc_info=True)
            raise NebulaException(f"使用browser服务获取资源失败: {str(e)}")

        # 下载视频文件
        video_path = self.settings.video.video_path.format(platform=from_platform, unique_id=unique_id, ext=".mp4")
        s3_video_url = None
        # 下载视频
        logger.info(f"开始下载视频: {player_url}")
        try:
            timeout = aiohttp.ClientTimeout(total=self.settings.video.download_timeout)
            async with aiohttp.ClientSession(headers={"User-Agent": USER_AGENT}, timeout=timeout) as session:
                async with session.get(player_url) as response:
                    video_data = None
                    logger.info(f"服务器响应: {response.status} ({response.content_type})")
                    if response.status == 200:
                        # 判断content-type是否为视频类型
                        if not response.content_type.startswith("video/"):
                            if response.content_type == "application/json":
                                logger.info(await response.read())
                                # x.fee.red youtube api下载给多一点时间能成功
                                raise YoutubeException(f"Youtube.Proxy 下载视频失败: {response.status} ({response.content_type})")
                            raise NebulaException(f"不支持的文件类型: {response.content_type}")
                        logger.info(f"开始读取视频数据")
                        logger.info(f"{player_url}")
                        logger.info(f"{response.content_type}")
                        logger.info(f"response.headers: {response.headers}")

                        # 获取Content-Length用于进度跟踪
                        content_length = response.headers.get('Content-Length')
                        if content_length:
                            content_length = int(content_length)
                            logger.info(f"预期文件大小: {content_length} bytes")

                        # 分块读取视频数据，避免ClientPayloadError
                        video_data = bytearray()
                        chunk_size = 8192  # 8KB chunks
                        total_read = 0

                        try:
                            while True:
                                chunk = await response.content.read(chunk_size)
                                if not chunk:
                                    break
                                video_data.extend(chunk)
                                total_read += len(chunk)

                                # 每读取1MB记录一次进度
                                if total_read % (1024 * 1024) == 0:
                                    logger.info(f"已读取: {total_read} bytes")

                            video_data = bytes(video_data)
                            logger.info(f"视频数据读取完成，实际大小: {len(video_data)} bytes")

                            # 验证数据完整性
                            if content_length and len(video_data) != content_length:
                                logger.warning(f"数据长度不匹配: 预期 {content_length}, 实际 {len(video_data)}")

                        except aiohttp.ClientPayloadError as e:
                            logger.error(f"读取视频数据时发生ClientPayloadError: {str(e)}")
                            logger.info(f"已读取部分数据: {total_read} bytes")
                            # 如果已经读取了一些数据，尝试使用部分数据
                            if total_read > 0:
                                video_data = bytes(video_data)
                                logger.warning(f"使用部分读取的数据: {len(video_data)} bytes")
                            else:
                                # 如果没有读取到任何数据，抛出异常触发browser服务回退
                                raise NebulaException(f"读取视频数据失败: {str(e)}", should_retry=True)
                        # 计算视频大小
                        video_size = len(video_data)
                        logger.info(f"视频(url:{player_url})下载成功 (size:{video_size})")
                    elif response.status == 403:
                        logger.info(f"平台风控机制导致403错误，尝试使用browser服务获取资源")
                        resource_result = await self.browser.get_resource_bytes(player_url)
                        if resource_result and resource_result.get("data"):
                            video_data = resource_result["data"]
                            # 计算视频大小
                            video_size = len(video_data)
                            logger.info(f"视频(url:{player_url})下载成功 (size:{video_size})")
                        else:
                            raise ExternalServiceError("BrowserService", f"使用browser服务获取资源失败: {player_url}", should_retry=True)
                    else:
                        await self.storage.delete_object(cover_path) # 存储回滚
                        raise NebulaException(f"下载视频: 服务器返回状态码 {response.status}", should_retry=True)

                    # 上传到存储服务
                    s3_video_url = await self.storage.upload_bytes(
                        data=video_data,
                        object_name=video_path,
                        bucket_name=self.bucket
                    )
                    logger.info(f"已保存到S3存储服务({s3_video_url})")

        except Exception as e:
            v_delete = await self.storage.delete_object(cover_path, bucket_name=self.bucket)
            logger.info(f"删除封面: {cover_path} ({v_delete})")
            if isinstance(e, asyncio.exceptions.TimeoutError):
                raise TimeoutError(f"网络请求超时", should_retry=True)
            elif isinstance(e, aiohttp.ClientPayloadError):
                logger.error(f"HTTP请求数据传输错误，尝试使用browser服务: {str(e)}")
                # 尝试使用browser服务作为回退方案
                try:
                    resource_result = await self.browser.get_resource_bytes(player_url)
                    if resource_result and resource_result.get("data"):
                        video_data = resource_result["data"]
                        video_size = len(video_data)
                        logger.info(f"使用browser服务下载成功 (size:{video_size})")

                        # 上传到存储服务
                        s3_video_url = await self.storage.upload_bytes(
                            data=video_data,
                            object_name=video_path,
                            bucket_name=self.bucket
                        )
                        logger.info(f"已保存到S3存储服务({s3_video_url})")
                    else:
                        raise ExternalServiceError("BrowserService", f"使用browser服务获取资源失败: {player_url}", should_retry=True)
                except Exception as browser_e:
                    logger.error(f"browser服务回退也失败: {str(browser_e)}", exc_info=True)
                    raise NebulaException(f"下载视频失败，HTTP和browser服务都失败: {str(e)}", should_retry=True)
            else:
                logger.error(f"下载视频失败: {str(e)}", exc_info=True)
                raise e

        try:
            # 创建视频记录
            video = Entity.Video(
                title=title,
                video_desc=desc,
                video_path=video_path,
                cover_path=cover_path,
                cover_url=s3_cover_url,
                video_size=video_size,
                video_id=video_id,
                play_url=s3_video_url,
                source_url=watch_url,
                platform=from_platform
            )
            
            return await self.create_video(video)
        except Exception as e:
            # 回滚storage
            await self.storage.delete_object(cover_path)
            await self.storage.delete_object(video_path)
            logger.error(f"从分享链接导入视频失败: {str(e)}", exc_info=True)
            raise NebulaException(f"从分享链接导入视频失败: {str(e)}")
