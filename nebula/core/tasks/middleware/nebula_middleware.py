"""
Nebula任务中间件

处理任务执行前后的逻辑，包括日志记录、通知发送等。
"""

import logging
import time
from typing import Any, Dict, Type
from taskiq import TaskiqMessage, TaskiqResult, TaskiqMiddleware

logger = logging.getLogger(__name__)

class NebulaTaskMiddleware(TaskiqMiddleware):
    """Nebula任务中间件

    处理任务执行前后的逻辑，包括日志记录、通知发送等。
    """

    def __init__(self):
        """初始化中间件"""
        from nebula.core.service.notification import NotificationService
        from nebula.core.service.taskman import TaskService
        from nebula.core.repository.taskinfo import Entity as TaskEntity
        from nebula.core.infrastructure.message import MessageService
        from nebula.core.infrastructure import provider

        self.message_service = provider.get(MessageService)
        self.notification_service = provider.get(NotificationService)
        self.task_service = provider.get(TaskService)
        self.task_status = TaskEntity.TaskStatus

        logger.info("NebulaTaskMiddleware initialized")

    async def pre_execute(self, message):
        """任务执行前的处理

        Args:
            message: 任务消息

        Returns:
            处理后的任务消息
        """
        logger.info(f"-----------------任务 [{message.task_name}] 准备执行------------------")
        # 更新任务状态为运行中
        if self.task_service:
            try:
                await self.task_service.update_task_status(
                    message.task_id,
                    self.task_status.RUNNING
                )
            except Exception as e:
                logger.error(f"更新任务状态失败: {str(e)}")

        return message

    async def post_execute(self, message, result):
        """任务执行后的处理 (包括重试策略)

        Args:
            message: 任务消息
            result: 任务结果

        Returns:
            处理后的任务结果
        """
        print ("*"*100)
        print (message.labels)
        print ("-"*100)
        print (result)
        print ("*"*100)
        logger.info(f"-----------------任务 [{message.task_name}] 执行结束------------------")

        # 每一次尝试执行的结果
        await self.on_each_end(message, result)

        if result.labels.get("_terminated", True):
            # 任务执行结束,包括重试策略
            await self.on_terminated(message, result)

        return result
    
    async def on_each_end(self, message: TaskiqMessage, result: TaskiqResult[Any]) -> None:
        """

        """
        next_retry_at = result.labels.get("_next_retry_at")
        if result.error and not result.labels.get("_terminated", False):
            # 错误且没有终止信号, 增加重试
            await self.task_service.increment_retry_count(message.task_id, result.error, next_retry_at)
        else:
            await self.task_service.update_task_status(
                message.task_id,
                self.task_status.SUCCESS if not result.error else self.task_status.FAILED,
                error_message=str(result.error) if result.error else None,
                result=result.return_value
            )

    async def on_terminated(self, message: TaskiqMessage, result: TaskiqResult[Any]) -> None:
        """任务终止时的处理(包括重试和正常结束)

        Args:
            message: 任务消息
            result: 任务结果
        """
        if result.error:
            await self.on_task_failure(message, result)
        else:
            await self.on_task_success(message, result)

        from nebula.core.protocol import ResponseMode
        if result.labels.get("response_mode") == ResponseMode.WEBSOCKET.value:
            await self._send_websocket_event(message, result)

        if message.labels.get("notification"):
            await self._add_persistent_notification(message, result)

    async def on_task_failure(self, message: TaskiqMessage, result: TaskiqResult[Any]) -> None:
        """任务失败时的处理
        替代on_error事件(有可能多次触发)

        Args:
            message: 任务消息
            result: 任务结果
        """

        # 记录失败
        logger.error(f"任务 {message.task_name} 执行失败: {result.error}")

    async def on_task_success(self, message: TaskiqMessage, result: TaskiqResult[Any]) -> None:
        """任务成功时的处理

        Args:
            message: 任务消息
            result: 任务结果
        """

        # 记录成功
        logger.info(f"任务 {message.task_name} 执行成功")

    async def _add_persistent_notification(self, message: TaskiqMessage, result: TaskiqResult[Any]) -> None:
        """添加持久化通知

        Args:
            message: 任务消息
            result: 任务结果
        """

        notify = message.labels["notification"]
        title = notify["title"]
        source = notify["source"]
        result_type = "info"
        msg = ""
        if result.error:
            msg = f"失败: {result.error}"
            result_type = "error"
        else:
            if result.return_value["status"]:
                msg = f"成功: {result.return_value['message']}"
                result_type = "success"
            else:
                msg = f"失败: {result.return_value['message']}"
                result_type = "error"


        await self.notification_service.create_notification(
            user_id = "admin",
            title = title,
            message = msg,
            event = message.task_name,
            from_source = source,
            type = result_type,
            metadata = message.kwargs.get("metadata", {}),
            task_id = message.task_id,
            to_web_topic = "GENERAL",
            send_websocket = True,
        )

    async def _send_websocket_event(self, message: TaskiqMessage, result: TaskiqResult[Any]) -> None:
        """发送WebSocket通知

        Args:
            message: 任务消息
            result: 任务结果
        """

        if self.message_service:
            try:
                # 这里是websocket回调
                from nebula.core.protocol.web import WebNotification
                notify = {
                    "event": message.task_name.split(".")[-1].replace(":",".").upper(),
                    "from_id": message.task_id,
                    "response_mode": message.labels["response_mode"],
                    "result": None,
                    "to_web_topic": "GENERAL",
                    "failed": result.error is not None,
                    "type": "success"
                }

                if notify["failed"]:
                    notify["type"] = "error"
                    notify["text"] = str(result.error)
                else:
                    notify["result"] = result.return_value
                    notify["text"] = "任务处理完成"

                notification = WebNotification(
                    event=notify["event"],
                    from_id=notify["from_id"],
                    response_mode=notify["response_mode"],
                    failed=notify["failed"],
                    to_web_topic=notify["to_web_topic"],
                    text=notify["text"],
                    metadata=notify["result"]
                )

                await self.message_service.publish_typed_message(notification)
                logger.info(f"已发送WebSocket通知: GENERAL -> {notification.event}")
            except Exception as e:
                logger.error(f"发送WebSocket通知失败: {str(e)}", exc_info=True)
