"""
Nebula任务中间件

处理任务执行前后的逻辑，包括日志记录、通知发送等。
"""

import logging
import time
from typing import Any, Dict, Type
from taskiq import TaskiqMessage, TaskiqResult, TaskiqMiddleware
from nebula.core.tasks.context import task_id_var,retry_index_var,step_var

logger = logging.getLogger(__name__)

class LoggingMiddleware(TaskiqMiddleware):
    """Nebula任务中间件

    处理任务执行前后的逻辑，包括日志记录、通知发送等。
    """

    def __init__(self):
        logger.info("LoggingMiddleware initialized")


    async def pre_execute(self, message):
        """任务执行前的处理

        Args:
            message: 任务消息

        Returns:
            处理后的任务消息
        """
        
        task_id_var.set(message.task_id)
        retry_count = message.labels.get("_retries", 0)
        retry_index_var.set(retry_count)
        step_var.set(0)

        return super().pre_execute(message)

    async def post_execute(self, message, result):
        """任务执行后的处理

        Args:
            message: 任务消息
            result: 任务结果

        Returns:
            处理后的任务结果
        """
        task_id_var.set(None)
        retry_index_var.set(None)
        step_var.set(None)
        return super().post_execute(message, result)


