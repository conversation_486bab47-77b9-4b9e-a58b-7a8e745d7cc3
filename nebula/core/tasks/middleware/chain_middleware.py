"""
任务链中间件

扩展TaskiqMiddleware，为任务链提供专门的中间件支持。
"""

import logging
from typing import Any, Dict
from taskiq import TaskiqMessage, TaskiqResult, TaskiqMiddleware

logger = logging.getLogger(__name__)


class ChainMiddleware(TaskiqMiddleware):
    """任务链中间件
    
    为任务链提供专门的中间件支持，包括：
    - 任务链执行状态跟踪
    - 任务链步骤监控
    - 任务链断点续传支持
    - 任务链特定的日志记录
    """

    def __init__(self):
        """初始化任务链中间件"""
        logger.info("ChainMiddleware initialized")

    async def pre_execute(self, message: TaskiqMessage) -> TaskiqMessage:
        """任务链执行前的处理
        
        Args:
            message: 任务消息
            
        Returns:
            处理后的任务消息
        """
        # 检查是否为任务链
        if self._is_chain_task(message):
            completed_steps = message.labels.get("chain_completed_steps", 0)
            logger.info(f"[CHAIN] 准备执行任务链: {message.task_name}")
            
            # 检查是否为重试
            retry_count = message.labels.get("_retries", 0)
            if retry_count > 0:
                # 计算跳过的步骤
                logger.info(f"[CHAIN] 任务链重试 (第{retry_count}次)，跳过 (步骤{completed_steps}) 继续执行")
            
        return message

    async def post_execute(self, message: TaskiqMessage, result: TaskiqResult[Any]) -> TaskiqResult[Any]:
        """任务链执行后的处理
        
        Args:
            message: 任务消息
            result: 任务结果
            
        Returns:
            处理后的任务结果
        """
        # 检查是否为任务链
        if self._is_chain_task(message):
            await self._handle_chain_result(message, result)
            
        return result

    def _is_chain_task(self, message: TaskiqMessage) -> bool:
        """检查是否为任务链任务
        
        Args:
            message: 任务消息
            
        Returns:
            是否为任务链任务
        """
            
        # 检查是否有任务链相关的标签
        if message.labels.get("chain_name"):
            return True
            
        return False

    async def _handle_chain_result(self, message: TaskiqMessage, result: TaskiqResult[Any]) -> None:
        """处理任务链结果
        
        Args:
            message: 任务消息
            result: 任务结果
        """
        chain_name = message.labels.get("chain_name")
        
        if result.error:
            # 任务链执行失败
            failed_step = message.labels.get("chain_failed_step")
            failed_step_index = message.labels.get("chain_failed_step_index", 0)
            
            logger.error(f"[CHAIN] 任务链 '{chain_name}' 在步骤 {failed_step_index} ({failed_step}) 执行失败: {result.error}")
            
            # 记录断点续传信息
            completed_steps = message.labels.get("chain_completed_steps", 0)
            if completed_steps > 0:
                logger.info(f"[CHAIN] 已保存断点续传信息，下次重试将从步骤 {completed_steps + 1} 开始")
                
        else:
            # 任务链执行成功
            if result.return_value and isinstance(result.return_value, dict):
                steps_executed = result.return_value.get("steps_executed", 0)
                steps_skipped = result.return_value.get("steps_skipped", 0)
                
                if steps_skipped > 0:
                    logger.info(f"[CHAIN] 任务链 '{chain_name}' 执行成功 (跳过 {steps_skipped} 步，执行 {steps_executed} 步)")
                else:
                    logger.info(f"[CHAIN] 任务链 '{chain_name}' 执行成功 (执行 {steps_executed} 步)")
            else:
                logger.info(f"[CHAIN] 任务链 '{chain_name}' 执行成功")

    async def on_chain_step_start(self, message: TaskiqMessage, step_name: str, step_index: int) -> None:
        """任务链步骤开始时的回调
        
        Args:
            message: 任务消息
            step_name: 步骤名称
            step_index: 步骤索引
        """
        chain_name = message.labels.get("chain_name", "Unknown")
        total_steps = message.labels.get("chain_steps", 0)
        chain_current_step = message.labels.get("chain_current_step", 0)
        from nebula.core.tasks.context import step_var
        step_var.set(chain_current_step)
        
        logger.info(f"[CHAIN] {chain_name} - 开始执行步骤 {step_index}/{total_steps}: {step_name}")

    async def on_chain_step_success(self, message: TaskiqMessage, step_name: str, step_index: int, step_result: Dict[str, Any]) -> None:
        """任务链步骤成功时的回调
        
        Args:
            message: 任务消息
            step_name: 步骤名称
            step_index: 步骤索引
            step_result: 步骤结果
        """
        chain_name = message.labels.get("chain_name", "Unknown")
        total_steps = message.labels.get("chain_steps", 0)
        
        logger.info(f"[CHAIN] {chain_name} - 步骤 {step_index}/{total_steps} ({step_name}) 执行成功")

    async def on_chain_step_failure(self, message: TaskiqMessage, step_name: str, step_index: int, error: Exception) -> None:
        """任务链步骤失败时的回调
        
        Args:
            message: 任务消息
            step_name: 步骤名称
            step_index: 步骤索引
            error: 错误信息
        """
        chain_name = message.labels.get("chain_name", "Unknown")
        total_steps = message.labels.get("chain_steps", 0)
        
        logger.error(f"[CHAIN] {chain_name} - 步骤 {step_index}/{total_steps} ({step_name}) 执行失败: {error}")

    async def on_chain_complete(self, message: TaskiqMessage, result: Dict[str, Any]) -> None:
        """任务链完成时的回调
        
        Args:
            message: 任务消息
            result: 最终结果
        """
        chain_name = message.labels.get("chain_name", "Unknown")
        steps_executed = result.get("steps_executed", 0)
        
        logger.info(f"[CHAIN] 任务链 '{chain_name}' 完成，共执行 {steps_executed} 个步骤")

    async def on_chain_retry(self, message: TaskiqMessage, retry_count: int, from_step: int) -> None:
        """任务链重试时的回调
        
        Args:
            message: 任务消息
            retry_count: 重试次数
            from_step: 从哪个步骤开始重试
        """
        chain_name = message.labels.get("chain_name", "Unknown")
        
        logger.info(f"[CHAIN] 任务链 '{chain_name}' 第 {retry_count} 次重试，从步骤 {from_step + 1} 开始")
