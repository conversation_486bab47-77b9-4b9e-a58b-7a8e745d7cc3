"""
任务链模块

提供任务链式执行的功能，支持多个任务按顺序执行并共享重试策略。
支持直接复用现有的@broker.task装饰的任务函数。
"""
import logging
import json
from typing import List, Callable, Dict, Any, Optional, Union
from dataclasses import dataclass
from taskiq import Context, TaskiqDepends

logger = logging.getLogger(__name__)


@dataclass
class ChainTask:
    """任务链中的任务定义"""
    name: str
    task_func: Callable
    description: str = ""


class TaskChainComposer:
    """任务链组合器

    支持直接复用现有的@broker.task装饰的任务函数，无需重复定义。
    支持ChainMiddleware中间件回调。
    """

    def __init__(self, name: str, description: str = ""):
        """初始化任务链组合器

        Args:
            name: 任务链名称
            description: 任务链描述
        """
        self.name = name
        self.description = description
        self.tasks: List[ChainTask] = []
        self._chain_middlewares: List = []
    
    def add_task(self,
                 task_func: Callable,
                 name: Optional[str] = None,
                 description: Optional[str] = None
                 ) -> 'TaskChainComposer':
        """添加任务到链中

        Args:
            task_func: 现有的@broker.task装饰的任务函数
            name: 可选的任务名称，默认使用函数名

        Returns:
            自身，支持链式调用
        """
        # 使用全限定名，避免taskiq的__taskiq_original后缀
        if name:
            task_name = name
        else:
            # 获取原始函数名（去掉__taskiq_original后缀）
            func_name = task_func.__name__
            if func_name.endswith('__taskiq_original'):
                func_name = func_name[:-len('__taskiq_original')]

            # 构建全限定名：模块.函数名
            module_name = getattr(task_func, '__module__', '')
            if module_name:
                task_name = f"{module_name}:{func_name}"
            else:
                task_name = func_name

        description = getattr(task_func, '__doc__', '') or ''

        chain_task = ChainTask(
            name=task_name,
            task_func=task_func,
            description=description.split('\n')[0] if description else ''
        )

        self.tasks.append(chain_task)
        return self

    def _discover_chain_middlewares(self) -> None:
        """发现并缓存ChainMiddleware实例"""
        if not self._chain_middlewares:
            from nebula.core.tasks.broker.config import broker
            from nebula.core.tasks.middleware.chain_middleware import ChainMiddleware
            
            # 从broker中查找ChainMiddleware
            self._chain_middlewares = [m for m in broker.middlewares if isinstance(m, ChainMiddleware)]

    async def _call_middleware_callback(self, method_name: str, *args, **kwargs) -> None:
        """调用中间件回调方法"""
        for middleware in self._chain_middlewares:
            if hasattr(middleware, method_name):
                try:
                    await getattr(middleware, method_name)(*args, **kwargs)
                except Exception as e:
                    logger.error(f"ChainMiddleware callback {method_name} failed: {e}")
    
    async def execute(self, metadata: Dict[str, Any], context: Context) -> Dict[str, Any]:
        """执行任务链（支持断点续传）
        
        Args:
            metadata: 初始输入数据
            context: 任务上下文
            
        Returns:
            最终执行结果
            
        Raises:
            Exception: 任何步骤执行失败时抛出异常
        """
        # 发现并初始化中间件
        self._discover_chain_middlewares()

        retry_count = context.message.labels.get("_retries", 0)
        start_step = context.message.labels.get("chain_completed_steps", 0)
        # 检查是否为重试，如果是则从断点继续
        is_retry = retry_count > 0
        current_data = metadata.copy()
        results = {}

        if is_retry:
            # 从labels中恢复任务链状态
            results = json.loads(context.message.labels.get("chain_step_results", "{}"))
            saved_data = json.loads(context.message.labels.get("chain_current_data", "{}"))

            if start_step > 0:
                # 断点恢复 saved_data
                logger.info(f"断点, 数据恢复")
                await self._call_middleware_callback("on_chain_retry", context.message, retry_count, start_step)
                current_data.update(saved_data)
        
        
        # 在context中记录任务链信息
        context.message.labels["chain_name"] = self.name
        context.message.labels["chain_steps"] = len(self.tasks)
        context.message.labels["chain_current_step"] = start_step
        
        for i in range(start_step, len(self.tasks)):
            task = self.tasks[i]
            try:
                # 更新当前步骤
                context.message.labels["chain_current_step"] = i + 1
                context.message.labels["chain_current_step_name"] = task.name
                
                # logger.info(f"执行任务链步骤 {i+1}/{len(self.tasks)}: {task.name}")

                # 调用步骤开始回调
                await self._call_middleware_callback("on_chain_step_start", context.message, task.name, i + 1)

                # 执行任务
                task_result = await self._execute_task(task, current_data, context)

                # 调用步骤成功回调
                await self._call_middleware_callback("on_chain_step_success", context.message, task.name, i + 1, task_result)

                # 保存任务结果
                results[task.name] = task_result
                
                # 将任务结果合并到下一步的输入数据中
                current_data.update(task_result)
                
                # 保存进度到labels（用于断点续传）
                context.message.labels["chain_completed_steps"] = i + 1
                context.message.labels["chain_step_results"] = json.dumps(results or {})
                context.message.labels["chain_current_data"] = json.dumps(current_data or {})
                
            except Exception as e:

                # 调用步骤失败回调
                await self._call_middleware_callback("on_chain_step_failure", context.message, task.name, i + 1, e)

                # 在context中记录失败信息
                context.message.labels["chain_failed_step"] = task.name
                context.message.labels["chain_failed_step_index"] = i + 1
                # 保存当前进度，以便重试时从此处继续
                context.message.labels["chain_completed_steps"] = i  # 当前步骤失败，所以完成步骤数为i
                context.message.labels["chain_step_results"] = json.dumps(results or {})
                context.message.labels["chain_current_data"] = json.dumps(current_data or {})
                raise e
        
        # 标记任务链完成
        context.message.labels["chain_status"] = "completed"
        # 清理断点续传相关的labels
        context.message.labels.pop("chain_completed_steps", None)
        context.message.labels.pop("chain_step_results", None)
        context.message.labels.pop("chain_current_data", None)
        
        logger.info(f"任务链 {self.name} 执行完成")

        final_result = {
            "status": True,
            "message": f"任务链 {self.name} 执行完成",
            "chain_name": self.name,
            "steps_executed": len(self.tasks),
            "steps_skipped": start_step,  # 记录跳过的步骤数
            "results": results,
            "final_data": current_data
        }

        # 调用任务链完成回调
        await self._call_middleware_callback("on_chain_complete", context.message, final_result)

        return final_result
    
    async def _execute_task(self, task: ChainTask, data: Dict[str, Any], context: Context) -> Dict[str, Any]:
        """执行单个任务
        
        Args:
            task: 任务定义
            data: 输入数据
            context: 任务上下文
            
        Returns:
            任务执行结果
        """
        # 直接调用现有的任务函数
        # 这些函数已经被@broker.task装饰，我们需要调用其核心逻辑
        return await task.task_func(data, context)
