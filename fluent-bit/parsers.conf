[PARSER]
    Name        nebula_log
    Format      regex
    Regex       /^TASK-LOG >> \[(?<time>[^\]]*)\] \(task_id=(?<task_id>[^,]*), order_no=(?<order_no>[0-9]+), retry_index=(?<retry_index>[0-9]+), step=(?<step>[^\)]*)\) (?<level>[^ ]*) (?<name>[^ ]*) - (?<message>.*) << TASK-LOG$/m
    Time_Key    time
    Time_Format %Y-%m-%d %H:%M:%S.%L
    Time_Keep   Off
    Types       retry_index:integer order_no:integer step:integer


[MULTILINE_PARSER]
    name          nebula_multiline
    type          regex
    flush_timeout 5000
    rule      "start_state"   "/^TASK-LOG >> /"  "cont"
    rule      "cont"          "/^(?!TASK-LOG >>).*/"  "cont"
